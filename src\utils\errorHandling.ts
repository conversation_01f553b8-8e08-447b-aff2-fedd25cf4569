/**
 * Error handling utilities for progressive loading
 */

import { useAuthStore } from '@/stores/authStore'
import { useProgramStore } from '@/stores/programStore'
import { toast } from '@/components/ui/toast'

interface NetworkError extends Error {
  code?: string
  status?: number
  isNetworkError?: boolean
}

/**
 * Detects if an error is due to corrupted cache data
 */
export function isCorruptedCacheError(error: unknown): boolean {
  if (!(error instanceof Error)) {
    return false
  }

  // Check for JSON parsing errors (corrupted cache)
  if (
    error.message.includes('JSON.parse') ||
    error.message.includes('SyntaxError')
  ) {
    return true
  }

  // Check for type errors that might indicate corrupted data
  if (error.name === 'TypeError' && error.message.includes('Cannot read')) {
    return true
  }

  // Check for Zustand persistence errors
  if (error.message.includes('persist') || error.message.includes('storage')) {
    return true
  }

  return false
}

/**
 * Clears corrupted cache data
 */
export function clearCorruptedCache(storeName?: string): void {
  try {
    if (!storeName || storeName === 'auth') {
      // Clear auth store cache
      const authStore = useAuthStore.getState()
      authStore.clearUserInfoCache()
    }

    if (!storeName || storeName === 'program') {
      // Clear program store cache
      const programStore = useProgramStore.getState()
      programStore.clearAllCache()
    }

    // Clear localStorage items that might be corrupted
    if (typeof window !== 'undefined') {
      const keysToCheck = ['auth-store', 'program-store']
      keysToCheck.forEach((key) => {
        try {
          const item = localStorage.getItem(key)
          if (item) {
            // Try to parse it to check if it's valid JSON
            JSON.parse(item)
          }
        } catch {
          // If parsing fails, remove the corrupted item
          localStorage.removeItem(key)
          console.warn(`Removed corrupted cache item: ${key}`)
        }
      })
    }
  } catch (error) {
    console.error('Error clearing corrupted cache:', error)
  }
}

/**
 * Creates a user-friendly error message
 */
export function getUserFriendlyErrorMessage(error: unknown): string {
  if (!(error instanceof Error)) {
    return 'An unexpected error occurred'
  }

  const networkError = error as NetworkError

  // Network errors
  if (networkError.isNetworkError || !navigator.onLine) {
    return 'Unable to connect. Please check your internet connection.'
  }

  // API errors
  if (networkError.status) {
    switch (networkError.status) {
      case 401:
        return 'Your session has expired. Please log in again.'
      case 403:
        return "You don't have permission to access this resource."
      case 404:
        return 'The requested information could not be found.'
      case 500:
      case 502:
      case 503:
        return 'Our servers are experiencing issues. Please try again later.'
      default:
        return 'Something went wrong. Please try again.'
    }
  }

  // Timeout errors
  if (
    error.message.includes('timeout') ||
    error.message.includes('timed out')
  ) {
    return 'The request took too long. Please try again.'
  }

  // Cache errors
  if (isCorruptedCacheError(error)) {
    return "There was an issue with your saved data. We've cleared it and you can try again."
  }

  return 'Something went wrong. Please try again.'
}

/**
 * Shows a subtle error notification
 */
export function showErrorNotification(error: unknown): void {
  const message = getUserFriendlyErrorMessage(error)

  // Check if we're in a browser environment and toast is available
  if (typeof window !== 'undefined' && typeof toast === 'function') {
    toast({
      title: 'Notice',
      description: message,
      variant: 'subtle',
      duration: 4000,
    })
  } else {
    console.error('Error:', message)
  }
}

/**
 * Handles errors with automatic recovery
 */
export async function handleErrorWithRecovery(
  error: unknown,
  options?: {
    retry?: () => Promise<unknown>
    onCacheCleared?: () => void
    storeName?: string
  }
): Promise<void> {
  // Check if it's a corrupted cache error
  if (isCorruptedCacheError(error)) {
    clearCorruptedCache(options?.storeName)

    if (options?.onCacheCleared) {
      options.onCacheCleared()
    }

    // Attempt retry if provided
    if (options?.retry) {
      try {
        await options.retry()
      } catch (retryError) {
        // If retry fails, show error notification
        showErrorNotification(retryError)
      }
    }
  } else {
    // For other errors, just show notification
    showErrorNotification(error)
  }
}

/**
 * Wraps an async function with error recovery
 */
export function withErrorRecovery<
  T extends (...args: unknown[]) => Promise<unknown>,
>(
  fn: T,
  options?: {
    storeName?: string
    onCacheCleared?: () => void
  }
): T {
  return (async (...args: Parameters<T>) => {
    try {
      return await fn(...args)
    } catch (error) {
      await handleErrorWithRecovery(error, {
        retry: () => fn(...args),
        ...options,
      })
      throw error // Re-throw after handling
    }
  }) as T
}
