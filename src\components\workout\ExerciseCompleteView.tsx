'use client'

import React from 'react'

interface ExerciseCompleteViewProps {
  exerciseLabel?: string
  isLastExercise: boolean
  nextExerciseLabel?: string
}

export function ExerciseCompleteView({
  exerciseLabel,
  isLastExercise,
  nextExerciseLabel,
}: ExerciseCompleteViewProps) {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <div className="text-center">
        <div className="mb-6">
          <svg
            className="w-24 h-24 text-green-500 mx-auto"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Great job!</h2>
        <p className="text-lg text-gray-600 mb-1">
          {exerciseLabel || 'Exercise'} complete
        </p>
        <p className="text-sm text-gray-500">
          {isLastExercise
            ? 'Final exercise done!'
            : `Moving to ${nextExerciseLabel || 'next exercise'}...`}
        </p>
      </div>
    </div>
  )
}
