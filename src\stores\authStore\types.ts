/**
 * Types for auth store
 */

import type { LoginSuccessResult, LoginSuccessResultAlt } from '@/types'

export interface User {
  email: string
  name?: string
  firstName?: string
  lastName?: string
  id?: string
}

export interface CachedUserInfo {
  firstName?: string
  lastName?: string
  [key: string]: unknown // Allow additional fields from API
}

export interface UserInfoCache {
  data: CachedUserInfo | null
  timestamp: number
  version: number
}

export interface AuthState {
  // State
  user: User | null
  token: string | null
  refreshToken: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  hasHydrated: boolean
  lastActivity: number

  // Cache State
  cachedUserInfo: UserInfoCache | null
  cacheVersion: number

  // Actions
  setAuth: (data: LoginSuccessResult | LoginSuccessResultAlt) => Promise<void>
  logout: () => Promise<void>
  updateTokens: (token: string, refreshToken: string) => Promise<void>
  updateUser: (userData: Partial<User>) => void
  setUser: (user: User) => void
  setError: (error: string) => void
  clearError: () => void
  setLoading: (loading: boolean) => void
  setHasHydrated: (hasHydrated: boolean) => void
  updateLastActivity: () => void
  checkSessionTimeout: () => void

  // Cache Actions
  setCachedUserInfo: (data: CachedUserInfo) => void
  getCachedUserInfo: () => CachedUserInfo | null
  isCacheStale: () => boolean
  clearUserInfoCache: () => void
}
