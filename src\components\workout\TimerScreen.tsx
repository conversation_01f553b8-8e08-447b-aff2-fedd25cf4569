'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useWorkout } from '@/hooks/useWorkout'
import { RestTimer } from './RestTimer'

export interface TimerScreenProps {
  soundEnabled: boolean
  vibrationEnabled: boolean
  isBetweenSets?: boolean
}

export function TimerScreen({
  soundEnabled,
  vibrationEnabled,
  isBetweenSets = false,
}: TimerScreenProps) {
  const router = useRouter()
  const {
    currentExercise,
    getNextExercise,
    currentSetIndex,
    totalSets,
    isLastSet,
    isLastExercise,
    nextSet,
    getRestDuration,
    exercises,
  } = useWorkout()

  const nextExercise = getNextExercise()

  // Redirect to workout overview if no exercise data
  useEffect(() => {
    if (!currentExercise && exercises && exercises.length === 0) {
      router.push('/workout')
    }
  }, [currentExercise, exercises, router])

  const handleTimerComplete = () => {
    // Vibrate if enabled
    if (vibrationEnabled && 'vibrate' in navigator) {
      navigator.vibrate([100, 50, 100])
    }

    // Use setTimeout to ensure navigation happens after timer completion
    setTimeout(() => {
      if (isBetweenSets) {
        // For rest between sets, always go back to current exercise
        if (currentExercise?.Id) {
          router.push(`/workout/exercise/${currentExercise.Id}`)
        } else {
          router.push('/workout')
        }
      } else if (isLastSet && isLastExercise) {
        router.push('/workout/complete')
      } else if (isLastSet) {
        // Between exercises - nextExercise was already called before timer
        // Just navigate to the next exercise without changing state
        if (nextExercise?.Id) {
          router.push(`/workout/exercise/${nextExercise.Id}`)
        } else {
          // Fallback to workout overview if no next exercise
          router.push('/workout')
        }
      } else {
        // This shouldn't happen in between-exercises rest, but handle it
        nextSet()
        if (currentExercise?.Id) {
          router.push(`/workout/exercise/${currentExercise.Id}`)
        } else {
          router.push('/workout')
        }
      }
    }, 100) // Small delay to ensure timer state is fully updated
  }

  const handleSkip = () => {
    // Log current state for debugging
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log('[TimerScreen] Skip button clicked:', {
        isBetweenSets,
        currentSetIndex,
        totalSets,
        isLastSet,
        isLastExercise,
        currentExerciseId: currentExercise?.Id,
        nextExerciseId: nextExercise?.Id,
      })
    }

    // Vibrate if enabled
    if (vibrationEnabled && 'vibrate' in navigator) {
      navigator.vibrate([100, 50, 100])
    }

    if (isBetweenSets) {
      // For rest between sets, always go back to current exercise
      if (currentExercise?.Id) {
        router.push(`/workout/exercise/${currentExercise.Id}`)
      } else {
        router.push('/workout')
      }
    } else if (isLastSet && isLastExercise) {
      router.push('/workout/complete')
    } else if (isLastSet) {
      // Between exercises - nextExercise was already called before timer
      // Just navigate to the next exercise without changing state
      if (nextExercise?.Id) {
        router.push(`/workout/exercise/${nextExercise.Id}`)
      } else {
        // Fallback to workout overview if no next exercise
        router.push('/workout')
      }
    } else {
      // This shouldn't happen in between-exercises rest, but handle it
      nextSet()
      if (currentExercise?.Id) {
        router.push(`/workout/exercise/${currentExercise.Id}`)
      } else {
        router.push('/workout')
      }
    }
  }

  return (
    <div className="min-h-[100dvh] bg-gray-50 flex flex-col">
      {/* Rest Timer - Centered content */}
      <div className="flex-1 flex flex-col justify-center">
        <RestTimer
          onComplete={handleTimerComplete}
          onSkip={handleSkip}
          autoStart
          soundEnabled={soundEnabled}
          customDuration={getRestDuration()}
        />
      </div>

      {/* Bottom Bar with Skip Button */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
        <div className="p-4">
          {/* Skip Button */}
          <button
            onClick={handleSkip}
            className="w-full py-4 min-h-[44px] bg-orange-500 text-white rounded-lg font-semibold hover:bg-orange-600 transition-colors"
            aria-label="Skip rest timer"
          >
            Skip
          </button>
        </div>
      </div>
    </div>
  )
}
