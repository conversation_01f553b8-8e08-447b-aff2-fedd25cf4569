/**
 * Workout API Module
 *
 * Exports for workout API functionality
 */

// Export all read operations
export {
  getUserProgramInfo,
  getUserWorkout,
  getTodaysWorkout,
  getWorkoutDetails,
  getExerciseRecommendation,
  getWorkoutHistory,
  getExerciseAlternatives,
  getExerciseSets,
  getUserWorkoutProgramTimeZoneInfo,
} from './read'

// Export all write operations
export {
  saveWorkoutSet,
  completeWorkout,
  skipWorkout,
  swapExercise,
} from './write'

// Export types
export type {
  WorkoutTemplateApiResponse,
  WorkoutCompletionData,
  WorkoutCompletionResponse,
  ExerciseSwapRequest,
} from './types'

// Export utilities
export { handleWorkoutResponse } from './utils'

/**
 * Workout API methods
 * Handles workout fetching, exercise recommendations, set logging, and completion
 */
export const workoutApi = {
  // Read operations
  getUserProgramInfo: async () => {
    const { getUserProgramInfo } = await import('./read')
    return getUserProgramInfo()
  },
  getUserWorkout: async () => {
    const { getUserWorkout } = await import('./read')
    return getUserWorkout()
  },
  getTodaysWorkout: async () => {
    const { getTodaysWorkout } = await import('./read')
    return getTodaysWorkout()
  },
  getWorkoutDetails: async (workoutId: string) => {
    const { getWorkoutDetails } = await import('./read')
    return getWorkoutDetails(workoutId)
  },
  getExerciseRecommendation: async (
    exerciseId: number,
    isFromServer: boolean = true
  ) => {
    const { getExerciseRecommendation } = await import('./read')
    return getExerciseRecommendation(exerciseId, isFromServer)
  },
  getWorkoutHistory: async (limit: number = 10) => {
    const { getWorkoutHistory } = await import('./read')
    return getWorkoutHistory(limit)
  },
  getExerciseAlternatives: async (exerciseId: number) => {
    const { getExerciseAlternatives } = await import('./read')
    return getExerciseAlternatives(exerciseId)
  },
  getExerciseSets: async (exerciseId: number) => {
    const { getExerciseSets } = await import('./read')
    return getExerciseSets(exerciseId)
  },
  getUserWorkoutProgramTimeZoneInfo: async () => {
    const { getUserWorkoutProgramTimeZoneInfo } = await import('./read')
    return getUserWorkoutProgramTimeZoneInfo()
  },

  // Write operations
  saveWorkoutSet: async (setData: import('@/types').WorkoutLogSerieModel) => {
    const { saveWorkoutSet } = await import('./write')
    return saveWorkoutSet(setData)
  },
  completeWorkout: async (
    workoutData: import('./types').WorkoutCompletionData
  ) => {
    const { completeWorkout } = await import('./write')
    return completeWorkout(workoutData)
  },
  skipWorkout: async (reason?: string) => {
    const { skipWorkout } = await import('./write')
    return skipWorkout(reason)
  },
  swapExercise: async (currentExerciseId: number, newExerciseId: number) => {
    const { swapExercise } = await import('./write')
    return swapExercise(currentExerciseId, newExerciseId)
  },
}
