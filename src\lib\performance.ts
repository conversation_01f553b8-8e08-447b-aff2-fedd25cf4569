// Performance monitoring utilities for Dr. Muscle X PWA
import type {
  PerformanceEventTiming,
  LayoutShift,
  WindowWithGtag,
  PerformanceMetric,
} from './performanceTypes'
import { PERFORMANCE_TARGETS } from './performanceTypes'
import { analyzeBundleSize } from './bundleAnalyzer'
import { getMemoryUsage } from './memoryMonitor'
import {
  measureDuration,
  logPerformanceMetrics,
  measureComponentRender,
} from './performanceUtils'
import { PerformanceRatings } from './performanceRatings'

export {
  analyzeBundleSize,
  getMemoryUsage,
  PERFORMANCE_TARGETS,
  measureDuration,
  logPerformanceMetrics,
  measureComponentRender,
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = []

  private observers: Map<string, PerformanceObserver> = new Map()

  constructor() {
    if (typeof window !== 'undefined') {
      this.initializeObservers()
      this.measureCoreWebVitals()
    }
  }

  private initializeObservers() {
    // Largest Contentful Paint (LCP)
    if ('PerformanceObserver' in window) {
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const lastEntry = entries[entries.length - 1]
          if (lastEntry) {
            this.recordMetric(
              'LCP',
              lastEntry.startTime,
              PerformanceRatings.rateLCP(lastEntry.startTime)
            )
          }
        })
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
        this.observers.set('lcp', lcpObserver)
      } catch (e) {
        console.warn('LCP observer not supported')
      }

      // First Input Delay (FID)
      try {
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          entries.forEach((entry) => {
            const firstInputEntry = entry as unknown as PerformanceEventTiming
            if (firstInputEntry.processingStart && firstInputEntry.startTime) {
              const fid =
                firstInputEntry.processingStart - firstInputEntry.startTime
              this.recordMetric('FID', fid, PerformanceRatings.rateFID(fid))
            }
          })
        })
        fidObserver.observe({ entryTypes: ['first-input'] })
        this.observers.set('fid', fidObserver)
      } catch (e) {
        console.warn('FID observer not supported')
      }

      // Cumulative Layout Shift (CLS)
      try {
        let clsValue = 0
        const clsObserver = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            const layoutShiftEntry = entry as LayoutShift
            if (!layoutShiftEntry.hadRecentInput) {
              clsValue += layoutShiftEntry.value
            }
          })
          this.recordMetric(
            'CLS',
            clsValue,
            PerformanceRatings.rateCLS(clsValue)
          )
        })
        clsObserver.observe({ entryTypes: ['layout-shift'] })
        this.observers.set('cls', clsObserver)
      } catch (e) {
        console.warn('CLS observer not supported')
      }
    }
  }

  private measureCoreWebVitals() {
    // First Contentful Paint (FCP)
    if (window.performance && window.performance.getEntriesByName) {
      const paintEntries = window.performance.getEntriesByName(
        'first-contentful-paint'
      )
      if (paintEntries.length > 0 && paintEntries[0]) {
        const fcp = paintEntries[0].startTime
        this.recordMetric('FCP', fcp, PerformanceRatings.rateFCP(fcp))
      }
    }

    // Time to Interactive (TTI) - simplified measurement
    if (document.readyState === 'complete') {
      const tti = performance.now()
      this.recordMetric('TTI', tti, PerformanceRatings.rateTTI(tti))
    } else {
      window.addEventListener('load', () => {
        const tti = performance.now()
        this.recordMetric('TTI', tti, PerformanceRatings.rateTTI(tti))
      })
    }
  }

  recordMetric(
    name: string,
    value: number,
    rating: 'good' | 'needs-improvement' | 'poor'
  ) {
    const metric: PerformanceMetric = {
      name,
      value,
      rating,
      timestamp: Date.now(),
    }
    this.metrics.push(metric)

    // Send to analytics if available
    if (typeof window !== 'undefined') {
      const { gtag } = window as WindowWithGtag
      if (gtag) {
        gtag('event', 'web_vitals', {
          event_category: 'Performance',
          event_label: name,
          value: Math.round(value),
          metric_rating: rating,
        })
      }
    }
  }

  getMetrics(): PerformanceMetric[] {
    return [...this.metrics]
  }

  getMetric(name: string): PerformanceMetric | undefined {
    return this.metrics.find((m) => m.name === name)
  }

  // Custom performance marks for workout-specific metrics
  static mark(name: string) {
    if (window.performance && window.performance.mark) {
      window.performance.mark(name)
    }
  }

  measure(name: string, startMark: string, endMark?: string) {
    if (window.performance && window.performance.measure) {
      try {
        const measure = window.performance.measure(name, startMark, endMark)
        this.recordMetric(
          name,
          measure.duration,
          PerformanceRatings.rateCustomMetric(measure.duration)
        )
      } catch (e) {
        console.warn(`Failed to measure ${name}`)
      }
    }
  }

  // Clean up observers
  disconnect() {
    this.observers.forEach((observer) => observer.disconnect())
    this.observers.clear()
  }
}

// Singleton instance
let performanceMonitor: PerformanceMonitor | null = null

export function getPerformanceMonitor(): PerformanceMonitor {
  if (!performanceMonitor) {
    performanceMonitor = new PerformanceMonitor()
  }
  return performanceMonitor
}
