'use client'

import React, { Component, ErrorInfo, ReactNode } from 'react'
import { ProgramErrorState } from './ProgramErrorState'

interface Props {
  children: ReactNode
  fallback?: (error: Error, reset: () => void) => ReactNode
}

interface State {
  hasError: boolean
  error: Error | null
}

export class ProgramErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
    }
  }

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  public override componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error for debugging in development
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.error('Program error boundary caught error:', error, errorInfo)
    }

    // In production, this would send to error monitoring service
    if (
      typeof window !== 'undefined' &&
      window.location.hostname !== 'localhost'
    ) {
      // Send to error monitoring (e.g., Sentry)
      // This is where you'd integrate with your error monitoring service
      // Example: window.Sentry?.captureException(error, {
      //   extra: {
      //     error: error.message,
      //     stack: error.stack,
      //     componentStack: errorInfo.componentStack,
      //   }
      // })
    }
  }

  private reset = () => {
    this.setState({ hasError: false, error: null })
  }

  public override render() {
    const { hasError, error } = this.state
    const { fallback, children } = this.props

    if (hasError) {
      if (fallback) {
        return fallback(error!, this.reset)
      }

      return <ProgramErrorState error={error} onRetry={this.reset} />
    }

    return children
  }
}
