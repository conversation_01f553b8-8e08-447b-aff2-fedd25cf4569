# Dr. Muscle X - Architecture Documentation

## 🎯 Quick Start

**Dr. Muscle X**: Lightning-fast mobile-first PWA replacing slow MAUI app.

**Key Metrics:**

- Load Time: < 1s (LCP)
- Bundle Size: < 150KB initial JS
- Touch Response: < 50ms
- Test Coverage: 90%+

**Production:**

- URL: https://x.dr-muscle.com/
- API: https://drmuscle.azurewebsites.net
- GitHub: carl<PERSON><PERSON> (id: 39870118)

## 🔧 Tech Stack

| Technology   | Version | Purpose                   | Config File            |
| ------------ | ------- | ------------------------- | ---------------------- |
| Next.js      | 15.1.6  | Framework with App Router | `next.config.js`       |
| React        | 19.0.0  | UI Library                | -                      |
| TypeScript   | 5.8.3   | Type Safety               | `tsconfig.json`        |
| Tailwind CSS | 3.4.17  | Styling                   | `tailwind.config.ts`   |
| Zustand      | 5.0.3   | State Management          | `/src/stores/`         |
| React Query  | 5.62.10 | Server State              | `/src/hooks/`          |
| Axios        | 1.7.10  | HTTP Client               | `/src/api/client.ts`   |
| next-pwa     | 5.6.0   | PWA Features              | `next.config.js`       |
| Vitest       | 1.0.4   | Unit Testing              | `vitest.config.mjs`    |
| Playwright   | 1.49.1  | E2E Testing               | `playwright.config.ts` |

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router pages
├── components/            # Reusable React components
│   ├── auth/             # Auth components
│   ├── workout/          # Workout components
│   └── ui/               # Generic UI components
├── hooks/                # Custom React hooks
├── stores/              # Zustand state stores
├── api/                 # API integration layer
├── types/               # TypeScript definitions
├── lib/                 # Utilities and helpers
└── services/            # Business logic
```

## 🏗️ Core Patterns

### State Management

```typescript
1. Server State → React Query
2. Client State → Zustand with persistence
3. Component State → React useState/useReducer
```

### API Flow

```typescript
Component → Custom Hook → React Query → Axios Client → API
API Error → Axios Interceptor → Retry/Queue → User Feedback
```

### Performance

1. Progressive data loading
2. Cache-first strategy
3. Parallel requests
4. Dynamic imports
5. Font optimization

## 🔐 Authentication

```
Login → JWT Token → Store in Zustand → Axios Interceptor → API Calls
         ↓
      Refresh Token → Auto-refresh on 401
```

**OAuth Config:**

- Google: `707210235326-204je3om2b9im1irln4g1ib90uocr9gc`
- Apple Team ID: `7AAXZ47995`

## 🔄 Key Workflows

### Authentication

```
Login Page → Credentials → API → Store Token → Redirect
```

### Workout Execution

```
Program → Today's Workout → Start → Exercise Loop → Complete
                                    ↓
                            Log Set → Rest Timer → Next Set
```

### Data Loading

```
Check Cache → Show Cached → Fetch Fresh → Update UI
```

## 💾 State Management

### Zustand Store

```typescript
interface AuthState {
  // State
  user: User | null
  token: string | null

  // Actions
  login: (credentials) => Promise<void>
  logout: () => void
  refreshToken: () => Promise<void>
}
```

### Cache Expiration

- Program: 24 hours
- Workouts: 24 hours
- Recommendations: 1 hour

## 🌐 API Integration

See `docs/references/api-reference-guide.md` for:

- Base URLs and endpoints
- Request/response models
- Authentication flow
- Error handling patterns

**Interceptors:**

- Request: Add auth token
- Response: Handle 401, retry errors, queue offline

## 🎨 UI Patterns

### Mobile-First Rules

```css
Touch targets: min 44px
Typography: 16px base minimum
Spacing: 16px padding, 8px margin
```

### Common Patterns

1. Skeleton loaders
2. Pull-to-refresh
3. Fixed bottom actions
4. Haptic feedback
5. Progressive disclosure

## 📊 Performance

| Metric      | Target  | Current |
| ----------- | ------- | ------- |
| LCP         | < 1s    | ✓       |
| FID         | < 100ms | ✓       |
| CLS         | < 0.1   | ✓       |
| TTI         | < 1.5s  | ✓       |
| Bundle Size | < 150KB | ✓       |

## 🧪 Testing

### Coverage Requirements

- Business logic: 90%+
- UI components: 80%+
- Utilities: 95%+
- Overall: 85%+

### Test Types

```
Unit Tests (Vitest): Components, Hooks, Utils
Integration Tests: API, Auth flow
E2E Tests (Playwright): Full journeys, Mobile viewports
```

## 🚀 Development

### Commands

```bash
npm run dev          # Development
npm run typecheck    # TypeScript check
npm run lint         # ESLint
npm run test         # Unit tests
npm run build        # Production build
npm run analyze      # Bundle analysis
```

### Deployment

```
Push to main → GitHub Actions → Vercel → Production
```

## 📋 Quick Reference

### Important Files

1. `CLAUDE.md` - Workflow instructions
2. `docs/status.md` - Project status
3. `src/api/client.ts` - API config
4. `src/hooks/useWorkout.ts` - Main data hook
5. `src/stores/authStore.ts` - Auth state

### Common Issues

- **TypeScript errors**: Run `npm run typecheck`
- **Bundle size**: Run `npm run analyze`
- **PWA not updating**: Clear service worker
- **Auth issues**: Check token expiration

### Useful Snippets

```typescript
// Check online
navigator.onLine

// Haptic feedback
navigator.vibrate?.(10)

// Performance mark
performance.mark('interaction-start')
```
