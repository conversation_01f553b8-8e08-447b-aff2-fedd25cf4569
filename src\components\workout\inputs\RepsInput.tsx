import React from 'react'

interface RepsInputProps {
  reps: number
  onChange: (value: string) => void
  disabled?: boolean
  error?: string
  quickButtons?: number[]
}

const DEFAULT_QUICK_BUTTONS = [5, 8, 10, 12, 15]

export function RepsInput({
  reps,
  onChange,
  disabled = false,
  error,
  quickButtons = DEFAULT_QUICK_BUTTONS,
}: RepsInputProps) {
  return (
    <div>
      <label
        htmlFor="reps-input"
        className="block text-sm font-medium text-gray-700 mb-2"
      >
        Reps
      </label>
      <input
        id="reps-input"
        type="number"
        value={reps}
        onChange={(e) => onChange(e.target.value)}
        disabled={disabled}
        min={1}
        max={100}
        inputMode="numeric"
        className={`w-full px-4 py-3 text-lg border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
          error ? 'border-red-500' : 'border-gray-300'
        } ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
        aria-label="Reps"
        aria-invalid={!!error}
        aria-describedby={error ? 'reps-error' : undefined}
      />
      {error && (
        <p id="reps-error" className="mt-1 text-sm text-red-600" role="alert">
          {error}
        </p>
      )}
      <div className="flex gap-2 mt-3">
        {quickButtons.map((quickReps) => (
          <button
            key={quickReps}
            type="button"
            onClick={() => onChange(quickReps.toString())}
            disabled={disabled}
            className={`px-3 py-1 text-sm border rounded-md ${
              reps === quickReps
                ? 'bg-blue-500 text-white border-blue-500'
                : 'bg-white text-gray-700 border-gray-300'
            } ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'}`}
          >
            {quickReps}
          </button>
        ))}
      </div>
    </div>
  )
}
