'use client'

import { useEffect, useRef } from 'react'

interface RIRPickerProps {
  isOpen: boolean
  onSelect: (rirValue: string) => void
  onCancel: () => void
}

export function RIRPicker({ isOpen, onSelect, onCancel }: RIRPickerProps) {
  const modalRef = useRef<HTMLDivElement>(null)
  const firstButtonRef = useRef<HTMLButtonElement>(null)

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onCancel()
      }
    }
    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [isOpen, onCancel])

  // Focus management
  useEffect(() => {
    if (isOpen) {
      setTimeout(() => firstButtonRef.current?.focus(), 100)
    }
  }, [isOpen])

  if (!isOpen) return null

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onCancel()
    }
  }

  const rirOptions = [
    { value: '0', label: 'Very hard (0 left)' },
    { value: '1-2', label: 'Could do 1-2 more' },
    { value: '3-4', label: 'Could do 3-4 more' },
    { value: '5-6', label: 'Could do 5-6 more' },
    { value: '7+', label: 'Could do 7+ more' },
  ]

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4"
      onClick={handleBackdropClick}
      role="presentation"
    >
      <div
        ref={modalRef}
        role="dialog"
        aria-modal="true"
        aria-labelledby="rir-title"
        className="w-full max-w-md rounded-2xl bg-white p-6 shadow-2xl"
        onClick={(e) => e.stopPropagation()}
      >
        <h2 id="rir-title" className="mb-6 text-center text-xl font-bold">
          How many more reps could you do?
        </h2>

        <div className="space-y-3">
          {rirOptions.map((option, index) => (
            <button
              key={option.value}
              ref={index === 0 ? firstButtonRef : undefined}
              onClick={() => onSelect(option.value)}
              className="w-full rounded-lg border border-gray-200 bg-white p-4 text-left transition-all hover:border-blue-500 hover:bg-blue-50 focus:border-blue-500 focus:bg-blue-50 focus:outline-none"
              aria-label={`Select RIR ${option.label}`}
            >
              <span className="block text-lg font-medium">{option.label}</span>
            </button>
          ))}
        </div>

        <button
          onClick={onCancel}
          className="mt-6 w-full rounded-lg bg-gray-100 py-3 font-medium text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300"
        >
          Cancel
        </button>
      </div>
    </div>
  )
}
