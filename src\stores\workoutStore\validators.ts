/**
 * Validation functions for the workout store
 */

import type { WorkoutTemplateModel, RecommendationModel } from '@/types'
import type { GetUserWorkoutProgramTimeZoneInfoResponse } from '@/services/api/workout'
import { logger } from '@/utils/logger'

// Helper functions for validation
export const isValidUserProgramInfo = (
  data: unknown
): data is GetUserWorkoutProgramTimeZoneInfoResponse => {
  if (data === null || typeof data !== 'object') {
    return false
  }

  const obj = data as Record<string, unknown>

  // Check if GetUserProgramInfoResponseModel exists
  if (
    !('GetUserProgramInfoResponseModel' in obj) ||
    obj.GetUserProgramInfoResponseModel === null ||
    typeof obj.GetUserProgramInfoResponseModel !== 'object'
  ) {
    return false
  }

  const programInfo = obj.GetUserProgramInfoResponseModel as Record<
    string,
    unknown
  >

  // Check for RecommendedProgram
  if (
    !('RecommendedProgram' in programInfo) ||
    programInfo.RecommendedProgram === null ||
    typeof programInfo.RecommendedProgram !== 'object'
  ) {
    return false
  }

  const recommendedProgram = programInfo.RecommendedProgram as Record<
    string,
    unknown
  >

  return (
    'Id' in recommendedProgram &&
    'Label' in recommendedProgram &&
    typeof recommendedProgram.Id === 'number' &&
    typeof recommendedProgram.Label === 'string'
  )
}

export const isValidWorkoutTemplate = (
  data: unknown
): data is WorkoutTemplateModel => {
  if (data === null || typeof data !== 'object') {
    return false
  }

  const obj = data as Record<string, unknown>

  // More flexible validation - Id might be string or number
  const hasValidId =
    'Id' in obj && (typeof obj.Id === 'number' || typeof obj.Id === 'string')
  const hasValidLabel = 'Label' in obj && typeof obj.Label === 'string'
  // Accept both French (Exercices) and English (Exercises) field names
  const hasValidExercices =
    ('Exercices' in obj && Array.isArray(obj.Exercices)) ||
    ('Exercises' in obj && Array.isArray(obj.Exercises))

  if (!hasValidId || !hasValidLabel || !hasValidExercices) {
    logger.error('Workout template validation failed:', {
      hasValidId,
      hasValidLabel,
      hasValidExercices,
      id: obj.Id,
      idType: typeof obj.Id,
      label: obj.Label,
      labelType: typeof obj.Label,
      exercices: obj.Exercices || obj.Exercises,
      exercicesIsArray:
        Array.isArray(obj.Exercices) || Array.isArray(obj.Exercises),
    })
  }

  return hasValidId && hasValidLabel && hasValidExercices
}

export const isValidWorkoutTemplateArray = (
  data: unknown
): data is WorkoutTemplateModel[] => {
  return Array.isArray(data) && data.every(isValidWorkoutTemplate)
}

export const isValidRecommendation = (
  data: unknown
): data is RecommendationModel => {
  if (data === null || typeof data !== 'object') {
    return false
  }

  const obj = data as Record<string, unknown>

  if (!('Series' in obj) || !('Reps' in obj) || !('Weight' in obj)) {
    return false
  }

  if (typeof obj.Series !== 'number' || typeof obj.Reps !== 'number') {
    return false
  }

  if (obj.Weight === null || typeof obj.Weight !== 'object') {
    return false
  }

  const weight = obj.Weight as Record<string, unknown>
  return typeof weight.Lb === 'number' && typeof weight.Kg === 'number'
}
