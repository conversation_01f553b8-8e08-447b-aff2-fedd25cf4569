/**
 * Type definitions for workout API
 */

import type { GetUserProgramInfoResponseModel } from '@/types'

/**
 * Request model for getting exercise recommendations
 */
export interface GetRecommendationForExerciseRequest {
  Username: string
  ExerciseId: number
  WorkoutId: number
  IsQuickMode?: boolean
  LightSessionDays?: number | null
  SwapedExId?: number
  IsStrengthPhashe?: boolean // Note: API has typo
  IsFreePlan?: boolean
  IsFirstWorkoutOfStrengthPhase?: boolean
  VersionNo?: number
  SetStyle?: string
  IsFlexibility?: boolean
}

/**
 * Response model for GetUserWorkoutProgramTimeZoneInfo endpoint
 */
export interface GetUserWorkoutProgramTimeZoneInfoResponse {
  GetUserProgramInfoResponseModel?: GetUserProgramInfoResponseModel
  LastWorkoutDate?: string
  LastConsecutiveWorkoutDays?: number
  ProgramName?: string
  ProgramType?: string
  NextWorkoutDate?: string
  UserAge?: number
}
