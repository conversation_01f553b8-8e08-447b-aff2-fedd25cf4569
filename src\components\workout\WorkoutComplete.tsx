'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useWorkout } from '@/hooks/useWorkout'
import type { WorkoutSession } from '@/types'

export function WorkoutComplete() {
  const router = useRouter()
  const { workoutSession, finishWorkout, isLoading, error } = useWorkout()
  const [saveError, setSaveError] = useState<string | null>(null)

  // Calculate workout statistics
  const calculateStats = (session: WorkoutSession | null) => {
    if (!session) return null

    const totalSets = session.exercises.reduce(
      (sum, exercise) => sum + exercise.sets.length,
      0
    )

    const workingSets = session.exercises.reduce(
      (sum, exercise) =>
        sum + exercise.sets.filter((set) => !set.isWarmup).length,
      0
    )

    const totalVolume = session.exercises.reduce(
      (sum, exercise) =>
        sum +
        exercise.sets.reduce(
          (setSum, set) => setSum + set.reps * set.weight.Lb,
          0
        ),
      0
    )

    // Calculate average RIR from sets that have RIR data
    const rirValues = session.exercises.flatMap((exercise) =>
      exercise.sets
        .filter((set) => set.rir !== undefined)
        .map((set) => set.rir as number)
    )
    const avgRir =
      rirValues.length > 0
        ? rirValues.reduce((sum, rir) => sum + rir, 0) / rirValues.length
        : null

    return {
      totalExercises: session.exercises.length,
      totalSets,
      workingSets,
      totalVolume,
      avgRir,
    }
  }

  const stats = calculateStats(workoutSession)

  const formatVolume = (volume: number) => {
    // Format with comma separator
    return `${volume.toLocaleString('en-US')} lbs`
  }

  const handleFinishWorkout = async () => {
    try {
      setSaveError(null)
      await finishWorkout()
      router.push('/program')
    } catch (err) {
      setSaveError('Failed to save workout')
    }
  }

  // Check for personal records (mock data for now)
  const personalRecords =
    (
      useWorkout() as unknown as {
        personalRecords?: Array<{
          exercise: string
          type: string
          value: string
        }>
      }
    ).personalRecords || []

  // Check offline status
  const isOffline =
    (useWorkout() as unknown as { isOffline?: boolean }).isOffline || false

  if (!workoutSession) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-6">
        <p className="text-gray-600">No workout data</p>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* Celebration Animation */}
      <div
        data-testid="celebration-animation"
        className="absolute inset-0 pointer-events-none overflow-hidden"
      >
        {/* Add confetti or animation here */}
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-y-auto p-6 pb-32">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Nice work!</h1>
        </div>

        {/* Stats Container */}
        <div data-testid="workout-stats" className="space-y-6">
          {/* Summary Stats */}
          <div className="bg-white rounded-lg shadow-sm p-6 space-y-4">
            <h2 className="text-lg font-semibold text-gray-900">Summary</h2>

            {/* Exercises */}
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Exercises</span>
              <span className="font-medium" data-testid="total-exercises">
                {stats?.totalExercises || 0}
              </span>
            </div>

            {/* Sets */}
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Sets</span>
              <span className="font-medium" data-testid="total-sets">
                {stats?.totalSets || 0}
              </span>
            </div>

            {/* Total Volume */}
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Total Volume</span>
              <span className="font-medium">
                {stats ? formatVolume(stats.totalVolume) : '0 lbs'}
              </span>
            </div>

            {/* Average RIR */}
            {stats && stats.avgRir !== null && stats.avgRir !== undefined && (
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Avg RIR</span>
                <span className="font-medium">{stats.avgRir.toFixed(1)}</span>
              </div>
            )}
          </div>

          {/* Personal Records */}
          {personalRecords.length > 0 && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <p className="text-green-800 font-semibold mb-2">
                New Personal Record!
              </p>
              {personalRecords.map((pr) => (
                <p
                  key={`${pr.exercise}-${pr.type}`}
                  className="text-sm text-green-700"
                >
                  {pr.exercise} - {pr.type}: {pr.value}
                </p>
              ))}
            </div>
          )}

          {/* Offline Mode */}
          {isOffline && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <p className="text-blue-800">Offline mode</p>
              <p className="text-sm text-blue-600 mt-1">
                Your workout will sync when connected
              </p>
            </div>
          )}

          {/* Error Display */}
          {(error || saveError) && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-red-800">
                {error || saveError || 'An error occurred'}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Bottom Actions */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
        {error ? (
          <button
            onClick={handleFinishWorkout}
            className="w-full py-4 px-6 min-h-[56px] bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Retry
          </button>
        ) : (
          <button
            onClick={handleFinishWorkout}
            disabled={isLoading}
            className="w-full py-4 px-6 min-h-[56px] bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Saving...' : 'Back to Home'}
          </button>
        )}
      </div>
    </div>
  )
}
