/**
 * Logger utility for Dr<PERSON> Mu<PERSON>cle X
 * Only logs in development mode to keep production clean
 */

const isDevelopment = process.env.NODE_ENV === 'development'

export const logger = {
  log: (...args: unknown[]) => {
    if (isDevelopment) {
      // eslint-disable-next-line no-console
      console.log(...args)
    }
  },

  info: (...args: unknown[]) => {
    if (isDevelopment) {
      // eslint-disable-next-line no-console
      console.info(...args)
    }
  },

  warn: (...args: unknown[]) => {
    if (isDevelopment) {
      // eslint-disable-next-line no-console
      console.warn(...args)
    }
  },

  error: (...args: unknown[]) => {
    // Always log errors, even in production
    // eslint-disable-next-line no-console
    console.error(...args)
  },

  debug: (...args: unknown[]) => {
    if (isDevelopment) {
      // eslint-disable-next-line no-console
      console.debug(...args)
    }
  },

  group: (label: string) => {
    if (isDevelopment) {
      // eslint-disable-next-line no-console
      console.group(label)
    }
  },

  groupEnd: () => {
    if (isDevelopment) {
      // eslint-disable-next-line no-console
      console.groupEnd()
    }
  },

  table: (data: unknown) => {
    if (isDevelopment) {
      // eslint-disable-next-line no-console
      console.table(data)
    }
  },

  time: (label: string) => {
    if (isDevelopment) {
      // eslint-disable-next-line no-console
      console.time(label)
    }
  },

  timeEnd: (label: string) => {
    if (isDevelopment) {
      // eslint-disable-next-line no-console
      console.timeEnd(label)
    }
  },
}
