const withPWA = require('next-pwa')({
  dest: 'public',
  register: true,
  skipWaiting: true,
  disable: process.env.NODE_ENV === 'development',
  buildExcludes: [/middleware-manifest\.json$/],
  cacheStartUrl: false,
  dynamicStartUrl: false,
  customWorkerDir: 'worker',
  fallbacks: {
    document: '/offline', // Add offline page
  },
  disableDevLogs: true,
  runtimeCaching: [
    {
      urlPattern: /^https:\/\/api\.drmuscle\.com\/.*/i,
      handler: 'NetworkFirst',
      options: {
        cacheName: 'drmuscle-api-cache',
        expiration: {
          maxEntries: 32,
          maxAgeSeconds: 24 * 60 * 60, // 24 hours
        },
        networkTimeoutSeconds: 10,
      },
    },
    {
      urlPattern: /\.(?:eot|otf|ttc|ttf|woff|woff2|font\.css)$/i,
      handler: 'StaleWhileRevalidate',
      options: {
        cacheName: 'static-font-assets',
        expiration: {
          maxEntries: 4,
          maxAgeSeconds: 7 * 24 * 60 * 60, // 7 days
        },
      },
    },
  ],
});

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  poweredByHeader: false,
  compress: true,
  experimental: {
    // optimizeCss is causing issues, disable for now
  },
  images: {
    formats: ['image/avif', 'image/webp'],
    deviceSizes: [375, 414, 768, 1024, 1200], // Mobile-first device sizes
    imageSizes: [16, 32, 48, 64, 96, 128, 256], // Touch icon sizes
  },
  webpack: (config, { isServer }) => {
    // Bundle analyzer
    if (process.env.ANALYZE === 'true') {
      const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
      config.plugins.push(
        new BundleAnalyzerPlugin({
          analyzerMode: 'static',
          reportFilename: isServer
            ? '../analyze/server.html'
            : './analyze/client.html',
        })
      );
    }
    
    // Mobile-first optimizations
    config.optimization = {
      ...config.optimization,
      sideEffects: false,
    };
    
    return config;
  },
};

module.exports = withPWA(nextConfig);