import { useEffect, useState } from 'react'
import { restoreAuthToken } from '@/utils/auth/tokenRestore'

/**
 * Hook to restore auth token from httpOnly cookies
 * Returns loading state while token is being restored
 */
export function useAuthTokenRestore() {
  const [isRestoringToken, setIsRestoringToken] = useState(true)
  const [tokenRestored, setTokenRestored] = useState(false)

  useEffect(() => {
    let mounted = true

    const restore = async () => {
      try {
        const restored = await restoreAuthToken()
        if (mounted) {
          setTokenRestored(restored)
        }
      } catch (error) {
        console.error('[useAuthTokenRestore] Failed to restore token:', error)
        // Even on error, we should allow the app to continue
        if (mounted) {
          setTokenRestored(false)
        }
      } finally {
        if (mounted) {
          setIsRestoringToken(false)
        }
      }
    }

    restore()

    return () => {
      mounted = false
    }
  }, [])

  return {
    isRestoringToken,
    tokenRestored,
  }
}
