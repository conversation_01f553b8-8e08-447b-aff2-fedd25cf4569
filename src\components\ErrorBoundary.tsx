'use client'

import React, { Component, ErrorInfo, ReactNode } from 'react'

interface ErrorBoundaryProps {
  children: ReactNode
  fallback?: React.ComponentType<{
    error: Error
    errorInfo: ErrorInfo | null
    retry: () => void
  }>
  onError?: (error: Error, errorInfo: ErrorInfo) => void
  showDetails?: boolean
  context?: unknown
}

interface ErrorBoundaryState {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
  showDetails: boolean
}

export class ErrorBoundary extends Component<
  ErrorBoundaryProps,
  ErrorBoundaryState
> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false,
    }
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return { hasError: true, error }
  }

  override componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo)

    this.setState({ errorInfo })

    // Call onError callback if provided
    const { onError, context } = this.props
    if (onError) {
      const enhancedErrorInfo = {
        ...errorInfo,
        context,
      }
      onError(error, enhancedErrorInfo)
    }
  }

  handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false,
    })
  }

  static getErrorMessage(error: Error): {
    message: string
    isChunkError: boolean
  } {
    // Handle different error types
    if (error.name === 'NetworkError' || error.message.includes('fetch')) {
      return {
        message: 'Connection issue. Please check your internet.',
        isChunkError: false,
      }
    }
    if (
      error.name === 'ChunkLoadError' ||
      error.message.includes('Loading chunk')
    ) {
      return {
        message: 'update available. Please refresh the page.',
        isChunkError: true,
      }
    }
    if (error.name === 'AuthenticationError') {
      return {
        message: 'Your session has expired. Please log in again.',
        isChunkError: false,
      }
    }

    return {
      message: error.message || 'An unexpected error occurred.',
      isChunkError: false,
    }
  }

  toggleDetails = () => {
    this.setState((prev) => ({ showDetails: !prev.showDetails }))
  }

  override render() {
    const { hasError, error, errorInfo, showDetails } = this.state
    const { fallback, showDetails: showDetailsProps } = this.props

    if (hasError && error) {
      // Try to render custom fallback
      if (fallback) {
        const FallbackComponent = fallback
        // Render fallback component directly
        return (
          <FallbackComponent
            error={error}
            errorInfo={errorInfo}
            retry={this.handleReset}
          />
        )
      }

      // Default error UI
      const { message: errorMessage, isChunkError } =
        ErrorBoundary.getErrorMessage(error)

      return (
        <div className="min-h-[100dvh] flex items-center justify-center p-6">
          <div
            role="alert"
            className="bg-white rounded-lg shadow-lg p-6 max-w-md w-full"
          >
            <div className="text-center">
              {/* Error Icon */}
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                <svg
                  className="h-6 w-6 text-red-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                  />
                </svg>
              </div>

              {/* Error Title */}
              <h2 className="text-xl font-bold text-gray-900 mb-2">
                Oops! Something went wrong
              </h2>

              {/* Error Message */}
              <p className="text-gray-600 mb-6">
                We've encountered an unexpected error. {errorMessage}
              </p>

              {/* Action Buttons */}
              <div className="space-y-3">
                {isChunkError ? (
                  <button
                    onClick={() => window.location.reload()}
                    className="w-full h-14 px-8 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Refresh Page
                  </button>
                ) : (
                  <button
                    onClick={this.handleReset}
                    className="w-full h-14 px-8 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Try Again
                  </button>
                )}

                {/* Show Details Button */}
                {(showDetailsProps ||
                  process.env.NODE_ENV === 'development') && (
                  <button
                    onClick={this.toggleDetails}
                    className="w-full h-12 px-6 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                  >
                    {showDetails ? 'Hide' : 'Show'} Details
                  </button>
                )}
              </div>

              {/* Error Details */}
              {showDetails && (
                <div className="mt-6 text-left">
                  <div className="bg-gray-100 rounded-lg p-4 overflow-auto max-h-64">
                    <p className="text-sm font-mono text-gray-800 mb-2">
                      {error.message}
                    </p>
                    {errorInfo && (
                      <div>
                        <p className="text-sm font-semibold text-gray-700 mt-4 mb-1">
                          Component Stack:
                        </p>
                        <pre className="text-xs font-mono text-gray-600 whitespace-pre-wrap">
                          {errorInfo.componentStack}
                        </pre>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )
    }

    const { children } = this.props
    return children
  }
}
