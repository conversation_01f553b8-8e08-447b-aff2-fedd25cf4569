@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Dr. Muscle X Design System Theme Variables */
@import '../design-system/styles/theme-variables.css';

/* Typography Utilities for Subtle Depth Theme */
[data-theme='subtle-depth'] {
  /* Playfair Display Headings with Letter Spacing */
  .heading-luxury {
    font-family: var(--font-heading);
    letter-spacing: var(--letter-spacing-wider);
    text-shadow: var(--text-shadow-md);
  }

  .heading-luxury-lg {
    font-family: var(--font-heading);
    letter-spacing: var(--letter-spacing-widest);
    text-shadow: var(--text-shadow-lg);
  }

  /* Text Shadows */
  .text-shadow-sm {
    text-shadow: var(--text-shadow-sm);
  }

  .text-shadow-md {
    text-shadow: var(--text-shadow-md);
  }

  .text-shadow-lg {
    text-shadow: var(--text-shadow-lg);
  }

  .text-shadow-gold {
    text-shadow: var(--text-shadow-gold);
  }

  /* Letter Spacing Classes */
  .tracking-luxury {
    letter-spacing: var(--letter-spacing-wide);
  }

  .tracking-luxury-wide {
    letter-spacing: var(--letter-spacing-wider);
  }

  .tracking-luxury-widest {
    letter-spacing: var(--letter-spacing-widest);
  }

  /* Gradient Text */
  .text-gradient-gold {
    background: var(--gradient-metallic-gold);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .text-gradient-silver {
    background: var(--gradient-metallic-silver);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  /* Premium Background Gradients */
  .bg-premium {
    background: var(--gradient-bg-premium);
  }

  .bg-metallic-gold {
    background: var(--gradient-metallic-gold);
  }

  .bg-overlay-subtle {
    background: var(--gradient-overlay-subtle);
  }

  .bg-overlay-premium {
    background: var(--gradient-overlay-premium);
  }
}

/* Smooth theme transitions */
* {
  transition:
    background-color 300ms ease,
    color 300ms ease,
    border-color 300ms ease;
}

/* Disable transitions on theme change for instant feedback */
[data-theme-changing='true'] * {
  transition: none !important;
}

/* Font Loading States */
.fonts-loading .font-heading {
  visibility: hidden;
}

.fonts-loaded .font-heading {
  visibility: visible;
  animation: fontFadeIn 0.3s ease-in;
}

@keyframes fontFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Animation Classes */
.animate-fade-in {
  animation: fadeIn 0.7s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.7s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-1rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(2rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Shimmer Animation for Subtle Depth Theme */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(200%);
  }
}

[data-theme='subtle-depth'] {
  /* Shimmer Effect Class */
  .shimmer {
    position: relative;
    overflow: hidden;
  }

  .shimmer::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: var(--gradient-shimmer);
    animation: shimmer 2s linear infinite;
  }

  /* Shimmer on Hover */
  .shimmer-hover {
    position: relative;
    overflow: hidden;
  }

  .shimmer-hover::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: var(--gradient-shimmer);
    transform: translateX(-100%);
    transition: transform 0s;
  }

  .shimmer-hover:hover::after {
    animation: shimmer 2s linear infinite;
  }
}

/* Typography Defaults */
h1,
h2,
h3,
h4,
h5,
h6 {
  @apply font-heading;
}

/* PWA Safe Area Support */
.safe-area-top {
  height: env(safe-area-inset-top);
}

.safe-area-bottom {
  height: env(safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: env(safe-area-inset-right);
}

.safe-area-inset-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

/* Prevent iOS bounce effect while maintaining scrollability */
html {
  height: 100%;
}

body {
  height: 100%;
  margin: 0;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
  /* Apply theme colors with fallbacks */
  background-color: var(--color-bg-primary, #0a0a0b);
  color: var(--color-text-primary, #ffffff);
  font-family: var(--font-body);
}

/* Fix for small viewport height on mobile devices */
@supports (height: 100dvh) {
  html,
  body {
    height: 100dvh;
  }
}

/* Glassmorphism Utility Classes */
.glass-primary {
  background: var(--color-bg-glass-1, rgba(255, 255, 255, 0.08));
  backdrop-filter: blur(var(--glass-blur, 12px))
    saturate(var(--glass-saturation, 1.8));
  -webkit-backdrop-filter: blur(var(--glass-blur, 12px))
    saturate(var(--glass-saturation, 1.8));
  border: 1px solid var(--glass-border-light, rgba(255, 255, 255, 0.2));
  position: relative;
  overflow: hidden;
}

.glass-primary::before {
  content: '';
  position: absolute;
  inset: 0;
  background: var(
    --glass-gradient-1,
    linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.05) 100%
    )
  );
  pointer-events: none;
}

.glass-secondary {
  background: var(--color-bg-glass-2, rgba(255, 255, 255, 0.12));
  backdrop-filter: blur(calc(var(--glass-blur, 12px) * 0.8))
    saturate(var(--glass-saturation, 1.8));
  -webkit-backdrop-filter: blur(calc(var(--glass-blur, 12px) * 0.8))
    saturate(var(--glass-saturation, 1.8));
  border: 1px solid var(--glass-border-dark, rgba(255, 255, 255, 0.1));
  position: relative;
  overflow: hidden;
}

.glass-tertiary {
  background: var(--color-bg-glass-3, rgba(255, 255, 255, 0.16));
  backdrop-filter: blur(calc(var(--glass-blur, 12px) * 0.6))
    saturate(calc(var(--glass-saturation, 1.8) * 0.9));
  -webkit-backdrop-filter: blur(calc(var(--glass-blur, 12px) * 0.6))
    saturate(calc(var(--glass-saturation, 1.8) * 0.9));
  border: 1px solid var(--glass-border-light, rgba(255, 255, 255, 0.2));
}

/* Gradient Overlays for Glass Effects */
.glass-gradient-overlay::after {
  content: '';
  position: absolute;
  inset: 0;
  background:
    var(
      --glass-gradient-2,
      radial-gradient(
        circle at 20% 50%,
        rgba(0, 212, 255, 0.2) 0%,
        transparent 50%
      )
    ),
    var(
      --glass-gradient-3,
      radial-gradient(
        circle at 80% 50%,
        rgba(255, 0, 255, 0.2) 0%,
        transparent 50%
      )
    );
  pointer-events: none;
  mix-blend-mode: screen;
}

/* Glass Card Component */
.glass-card {
  @apply glass-primary rounded-2xl p-6 shadow-theme-lg;
}

/* Glass Button Component */
.glass-button {
  @apply glass-secondary px-6 py-3 rounded-theme font-semibold transition-all duration-300;
  border: 1px solid var(--glass-border-light, rgba(255, 255, 255, 0.2));
}

.glass-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  background: rgba(255, 255, 255, 0.15);
}

.glass-button:active {
  transform: translateY(0);
}
