'use client'

import { useEffect, useRef, useCallback } from 'react'
import { useTimer } from '@/hooks/useTimer'
import { useWorkoutStore } from '@/stores/workoutStore'

interface RestTimerProps {
  onComplete: () => void
  onSkip: () => void
  autoStart?: boolean
  soundEnabled?: boolean
  customDuration?: number
}

export function RestTimer({
  onComplete,
  onSkip,
  autoStart = false,
  soundEnabled = true,
  customDuration,
}: RestTimerProps) {
  const { getRestDuration, getExerciseProgress } = useWorkoutStore()

  const exerciseProgress = getExerciseProgress()
  const defaultDuration = customDuration ?? getRestDuration()

  const audioRef = useRef<HTMLAudioElement | null>(null)
  const wakeLockRef = useRef<WakeLockSentinel | null>(null)

  const { timeRemaining, isRunning, progress, formattedTime, skip } = useTimer(
    defaultDuration,
    {
      onComplete: () => {
        // Play completion sound
        if (soundEnabled && audioRef.current) {
          audioRef.current.play().catch(() => {
            // Ignore audio play errors
          })
        }
        onComplete()
      },
      autoStart,
    }
  )

  // Handle skip action
  const handleSkip = useCallback(() => {
    skip()
    onSkip()
  }, [skip, onSkip])

  // Request wake lock to prevent screen sleep
  useEffect(() => {
    const requestWakeLock = async () => {
      if ('wakeLock' in navigator && isRunning) {
        try {
          wakeLockRef.current = await navigator.wakeLock.request('screen')
        } catch (err) {
          // Wake lock request failed - ignore
        }
      }
    }

    requestWakeLock()

    return () => {
      if (wakeLockRef.current) {
        wakeLockRef.current.release()
        wakeLockRef.current = null
      }
    }
  }, [isRunning])

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 's' || e.key === 'S') {
        e.preventDefault()
        handleSkip()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [handleSkip])

  // Handle visibility change for background timer
  useEffect(() => {
    const handleVisibilityChange = () => {
      // Timer continues in background, no action needed
      // This is just for future enhancements if needed
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () =>
      document.removeEventListener('visibilitychange', handleVisibilityChange)
  }, [])

  // Initialize audio
  useEffect(() => {
    if (soundEnabled) {
      audioRef.current = new Audio('/sounds/timer-complete.mp3')
      audioRef.current.preload = 'auto'
    }

    return () => {
      if (audioRef.current) {
        audioRef.current = null
      }
    }
  }, [soundEnabled])

  // Get timer color based on remaining time
  const getTimerColor = () => {
    if (timeRemaining <= 5) return 'text-red-600'
    if (timeRemaining <= 10) return 'text-orange-600'
    return 'text-gray-900'
  }

  // Get rest type text
  const getRestTypeText = () => {
    if (exerciseProgress?.currentSetIsWarmup) return 'Warmup Rest'
    return 'Rest'
  }

  return (
    <div className="flex flex-col items-center justify-center flex-1 p-6">
      {/* Rest Type */}
      <h1 className="text-2xl font-bold text-gray-900 mb-8">
        {getRestTypeText()}
      </h1>

      {/* Progress Ring with Timer Inside */}
      <div className="relative w-48 h-48">
        <svg className="transform -rotate-90 w-48 h-48">
          <circle
            cx="96"
            cy="96"
            r="88"
            stroke="currentColor"
            strokeWidth="8"
            fill="none"
            className="text-gray-200"
          />
          <circle
            cx="96"
            cy="96"
            r="88"
            stroke="currentColor"
            strokeWidth="8"
            fill="none"
            strokeDasharray={`${2 * Math.PI * 88}`}
            strokeDashoffset={`${2 * Math.PI * 88 * (1 - progress / 100)}`}
            className="text-blue-500 transition-all duration-200"
            role="progressbar"
            aria-valuenow={Math.round(progress)}
            aria-valuemin={0}
            aria-valuemax={100}
          />
        </svg>
        {/* Timer Display Inside Circle */}
        <div
          role="timer"
          aria-live="polite"
          aria-label={`Rest time ${formattedTime} remaining`}
          className={`absolute inset-0 flex items-center justify-center text-4xl font-mono font-bold ${getTimerColor()}`}
        >
          {formattedTime}
        </div>
      </div>
    </div>
  )
}
