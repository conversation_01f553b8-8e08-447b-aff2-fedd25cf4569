import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import { WorkoutComplete } from '@/components/workout/WorkoutComplete'
import { useWorkout } from '@/hooks/useWorkout'
import { useRouter } from 'next/navigation'
import type { WorkoutSession } from '@/types'

// Mock dependencies
vi.mock('@/hooks/useWorkout')
vi.mock('next/navigation')

// Mock workout session data
const mockWorkoutSession: WorkoutSession = {
  id: 'session-1',
  startTime: new Date('2025-07-01T10:00:00'),
  endTime: new Date('2025-07-01T11:00:00'),
  exercises: [
    {
      exerciseId: 1,
      name: 'Bench Press',
      sets: [
        {
          setNumber: 1,
          reps: 10,
          weight: { Lb: 100, Kg: 45.4 },
          isWarmup: true,
        },
        {
          setNumber: 2,
          reps: 8,
          weight: { Lb: 120, Kg: 54.4 },
          isWarmup: false,
          rir: 2,
        },
        {
          setNumber: 3,
          reps: 8,
          weight: { Lb: 120, Kg: 54.4 },
          isWarmup: false,
        },
        {
          setNumber: 4,
          reps: 7,
          weight: { Lb: 120, Kg: 54.4 },
          isWarmup: false,
        },
      ],
    },
    {
      exerciseId: 2,
      name: 'Squat',
      sets: [
        {
          setNumber: 1,
          reps: 5,
          weight: { Lb: 200, Kg: 90.7 },
          isWarmup: false,
          rir: 1,
        },
        {
          setNumber: 2,
          reps: 5,
          weight: { Lb: 200, Kg: 90.7 },
          isWarmup: false,
        },
        {
          setNumber: 3,
          reps: 5,
          weight: { Lb: 200, Kg: 90.7 },
          isWarmup: false,
        },
      ],
    },
  ],
}

// Helper function to create complete useWorkout mocks
const createUseWorkoutMock = (
  overrides: Partial<ReturnType<typeof useWorkout>> = {}
) => ({
  todaysWorkout: undefined,
  currentWorkout: null,
  currentExercise: null,
  nextExercise: null,
  currentSetIndex: 0,
  totalSets: 0,
  isLastSet: false,
  isLastExercise: false,
  isWarmupSet: false,
  workoutSession: null,
  isLoadingWorkout: false,
  isLoading: false,
  workoutError: null,
  error: null,
  recommendation: null,
  refetchRecommendation: vi.fn(),
  saveSet: vi.fn(),
  startWorkout: vi.fn(),
  finishWorkout: vi.fn(),
  nextSet: vi.fn(),
  restoreWorkout: vi.fn(),
  goToNextExercise: vi.fn(),
  getRecommendation: vi.fn(),
  getRestDuration: vi.fn(),
  isOffline: false,
  ...overrides,
})

describe('WorkoutComplete', () => {
  const mockPush = vi.fn()
  const mockFinishWorkout = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()

    vi.mocked(useRouter).mockReturnValue({
      push: mockPush,
      back: vi.fn(),
      forward: vi.fn(),
      refresh: vi.fn(),
      prefetch: vi.fn(),
      replace: vi.fn(),
    } as ReturnType<typeof useRouter>)

    vi.mocked(useWorkout).mockReturnValue(
      createUseWorkoutMock({
        workoutSession: mockWorkoutSession,
        finishWorkout: mockFinishWorkout,
        isLoading: false,
        error: null,
      })
    )
  })

  describe('Workout Summary Display', () => {
    it('should display completion celebration UI', () => {
      // When
      render(<WorkoutComplete />)

      // Then
      expect(screen.getByText(/Nice work!/i)).toBeInTheDocument()
    })

    // Duration display removed from UI
    it.skip('should display workout duration', () => {
      // When
      render(<WorkoutComplete />)

      // Then
      expect(screen.getByText(/Duration/i)).toBeInTheDocument()
      expect(screen.getByText(/1 hour/i)).toBeInTheDocument()
    })

    it('should display total exercises completed', () => {
      // When
      render(<WorkoutComplete />)

      // Then
      expect(screen.getByText(/Exercises/i)).toBeInTheDocument()
      expect(screen.getByTestId('total-exercises')).toHaveTextContent('2')
    })

    it('should display total sets performed', () => {
      // When
      render(<WorkoutComplete />)

      // Then
      const setsLabel = screen.getAllByText(/Sets/i)[0] // Get the first occurrence
      expect(setsLabel).toBeInTheDocument()
      expect(screen.getByTestId('total-sets')).toHaveTextContent('7')
    })

    it('should display total volume', () => {
      // When
      render(<WorkoutComplete />)

      // Then
      expect(screen.getByText(/Total Volume/i)).toBeInTheDocument()
      // Calculate: (10*100 + 8*120 + 8*120 + 7*120) + (5*200 + 5*200 + 5*200) = 7760 lbs
      // But the actual calculation is 6760
      expect(screen.getByText('6,760 lbs')).toBeInTheDocument()
    })

    it('should handle missing workout session', () => {
      // Given
      vi.mocked(useWorkout).mockReturnValue(
        createUseWorkoutMock({
          workoutSession: null,
          finishWorkout: mockFinishWorkout,
          isLoading: false,
          error: null,
        })
      )

      // When
      render(<WorkoutComplete />)

      // Then
      expect(screen.getByText(/No workout data/i)).toBeInTheDocument()
    })
  })

  describe('Workout Completion Actions', () => {
    it('should show finish workout button', () => {
      // When
      render(<WorkoutComplete />)

      // Then
      expect(
        screen.getByRole('button', { name: /Back to Home/i })
      ).toBeInTheDocument()
    })

    it('should call finishWorkout on button click', async () => {
      // Given
      mockFinishWorkout.mockResolvedValue({ success: true })
      render(<WorkoutComplete />)

      // When
      const finishButton = screen.getByRole('button', {
        name: /Back to Home/i,
      })
      fireEvent.click(finishButton)

      // Then
      await waitFor(() => {
        expect(mockFinishWorkout).toHaveBeenCalled()
      })
    })

    it('should show loading state during save', async () => {
      // Given
      vi.mocked(useWorkout).mockReturnValue(
        createUseWorkoutMock({
          workoutSession: mockWorkoutSession,
          finishWorkout: mockFinishWorkout,
          isLoading: true,
          error: null,
        })
      )

      // When
      render(<WorkoutComplete />)

      // Then
      expect(screen.getByText(/Saving.../i)).toBeInTheDocument()
      const saveButton = screen.getByRole('button', { name: /Saving.../i })
      expect(saveButton).toBeDisabled()
    })

    it('should navigate to workout screen on success', async () => {
      // Given
      mockFinishWorkout.mockResolvedValue({ success: true })
      render(<WorkoutComplete />)

      // When
      const finishButton = screen.getByRole('button', {
        name: /Back to Home/i,
      })
      fireEvent.click(finishButton)

      // Then
      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/program')
      })
    })

    // Notes section removed from UI
    it.skip('should allow adding notes', () => {
      // When
      render(<WorkoutComplete />)

      // Then
      const notesInput = screen.getByPlaceholderText(/Add notes/i)
      expect(notesInput).toBeInTheDocument()

      // When - Type notes
      fireEvent.change(notesInput, { target: { value: 'Great workout!' } })

      // Then
      expect(notesInput).toHaveValue('Great workout!')
    })
  })

  describe('Error Handling', () => {
    it('should display error message on save failure', async () => {
      // Given
      mockFinishWorkout.mockRejectedValue(new Error('Network error'))
      render(<WorkoutComplete />)

      // When
      const finishButton = screen.getByRole('button', {
        name: /Back to Home/i,
      })
      fireEvent.click(finishButton)

      // Then
      await waitFor(() => {
        expect(screen.getByText(/Failed to save workout/i)).toBeInTheDocument()
      })
    })

    it('should show retry button on error', async () => {
      // Given
      vi.mocked(useWorkout).mockReturnValue(
        createUseWorkoutMock({
          workoutSession: mockWorkoutSession,
          finishWorkout: mockFinishWorkout,
          isLoading: false,
          error: 'Save failed',
        })
      )

      // When
      render(<WorkoutComplete />)

      // Then
      expect(screen.getByRole('button', { name: /Retry/i })).toBeInTheDocument()
    })

    it('should handle offline scenario', () => {
      // Given
      vi.mocked(useWorkout).mockReturnValue(
        createUseWorkoutMock({
          workoutSession: mockWorkoutSession,
          finishWorkout: mockFinishWorkout,
          isLoading: false,
          error: null,
          isOffline: true,
        })
      )

      // When
      render(<WorkoutComplete />)

      // Then
      expect(screen.getByText(/Offline mode/i)).toBeInTheDocument()
      expect(screen.getByText(/will sync when connected/i)).toBeInTheDocument()
    })
  })

  describe('Statistics Calculation', () => {
    it('should calculate workout statistics correctly', () => {
      // When
      render(<WorkoutComplete />)

      // Then - Check various stats
      const statsContainer = screen.getByTestId('workout-stats')
      expect(statsContainer).toBeInTheDocument()

      // Should show average RIR for exercises with RIR data
      expect(screen.getByText(/Avg RIR/i)).toBeInTheDocument()
      expect(screen.getByText('1.5')).toBeInTheDocument() // (2 + 1) / 2
    })

    it('should handle partial workout completion', () => {
      // Given - Workout with some incomplete sets
      const partialSession: WorkoutSession = {
        ...mockWorkoutSession,
        exercises: [
          {
            exerciseId: 1,
            name: 'Bench Press',
            sets: [
              {
                setNumber: 1,
                reps: 10,
                weight: { Lb: 100, Kg: 45.4 },
                isWarmup: true,
              },
              {
                setNumber: 2,
                reps: 8,
                weight: { Lb: 120, Kg: 54.4 },
                isWarmup: false,
              },
              // Missing sets 3 and 4
            ],
          },
        ],
      }

      vi.mocked(useWorkout).mockReturnValue(
        createUseWorkoutMock({
          workoutSession: partialSession,
          finishWorkout: mockFinishWorkout,
          isLoading: false,
          error: null,
        })
      )

      // When
      render(<WorkoutComplete />)

      // Then - Partial workout box is removed, just verify normal stats display
      expect(screen.getByText(/Nice work!/i)).toBeInTheDocument()
      expect(screen.getByTestId('total-exercises')).toHaveTextContent('1')
      expect(screen.getByTestId('total-sets')).toHaveTextContent('2')
    })
  })

  describe('Mobile Optimization', () => {
    it('should have mobile-friendly layout', () => {
      // When
      render(<WorkoutComplete />)

      // Then
      const finishButton = screen.getByRole('button', {
        name: /Back to Home/i,
      })
      expect(finishButton).toHaveClass('min-h-[56px]')
      expect(finishButton).toHaveClass('w-full')
    })

    it('should have large touch targets', () => {
      // When
      render(<WorkoutComplete />)

      // Then
      const buttons = screen.getAllByRole('button')
      buttons.forEach((button) => {
        // Main action button uses 56px, others use 44px minimum
        expect(button.className).toMatch(/min-h-\[(44|56)px\]/)
      })
    })
  })

  describe('Celebration Features', () => {
    it('should show celebration animation', () => {
      // When
      render(<WorkoutComplete />)

      // Then
      expect(screen.getByTestId('celebration-animation')).toBeInTheDocument()
    })

    it('should show personal records if achieved', () => {
      // Given - Mock PR data
      vi.mocked(useWorkout).mockReturnValue(
        createUseWorkoutMock({
          workoutSession: mockWorkoutSession,
          finishWorkout: mockFinishWorkout,
          isLoading: false,
          error: null,
          personalRecords: [
            { exercise: 'Bench Press', type: 'Weight', value: '120 lbs' },
          ],
        })
      )

      // When
      render(<WorkoutComplete />)

      // Then
      expect(screen.getByText(/New Personal Record!/i)).toBeInTheDocument()
      expect(screen.getByText(/Bench Press.*120 lbs/i)).toBeInTheDocument()
    })
  })
})
