import React from 'react'

interface FixedCTAButtonProps {
  onClick: () => void
  label?: string
  ariaLabel?: string
}

export function FixedCTAButton({
  onClick,
  label = 'Open Workout',
  ariaLabel = 'Open Workout - Start your next workout session',
}: FixedCTAButtonProps) {
  return (
    <div
      data-testid="cta-container"
      className="fixed bottom-0 left-0 right-0 p-4 bg-bg-primary border-t border-brand-primary/10"
      role="navigation"
      aria-label="Primary actions"
    >
      <button
        onClick={onClick}
        className="w-full min-h-[56px] bg-brand-primary text-text-inverse font-semibold rounded-theme hover:bg-brand-primary/90 active:scale-[0.98] shadow-theme-lg hover:shadow-theme-xl transition-all focus:outline-none focus:ring-2 focus:ring-brand-primary/50 focus:ring-offset-2 focus:ring-offset-bg-primary"
        aria-label={ariaLabel}
      >
        {label}
      </button>
    </div>
  )
}
