# Workout Recommendation Loading Flow for Web App

## Overview

The mobile app uses a two-phase loading strategy:

1. **Home Page**: Loads basic workout program information without exercise recommendations
2. **Workout Page**: Loads complete exercise recommendations for all exercises in the workout

## API Endpoints

Here are the 8 main API endpoints you'll need:

### 1. Get User Workout Program (Home Page)

```
POST /api/Exercise/GetUserWorkoutProgramTimeZoneInfo
Body: TimeZoneInfo object
Returns: WorkoutLogAverage with program info
```

### 2. Get Recommendation for Normal Sets

```
POST /api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew
Body: GetRecommendationForExerciseModel
Returns: RecommendationModel
```

### 3. Get Recommendation for Rest-Pause Sets

```
POST /api/Exercise/GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew
Body: GetRecommendationForExerciseModel
Returns: RecommendationModel
```

### 4. Save Workout Info

```
POST /api/Exercise/SaveGetWorkoutInfo
Body: SaveWorkoutModel { WorkoutId, WorkoutTemplateId }
Returns: Updated workout info
```

### 5. Get Exercise History

```
POST /api/Exercise/GetExerciseWorkoutHistory
Body: Exercise ID (long)
Returns: List<HistoryModel>
```

### 6. Get Workout History

```
POST /api/Exercise/GetWorkoutHistory
Body: null
Returns: List<HistoryModel>
```

### 7. Get User Workout Log Date

```
POST /api/Exercise/GetUserWorkoutLogDate
Body: null
Returns: List of workout dates
```

### 8. Get Completed Workout Weights Lifted

```
POST /api/Exercise/GetCompeletedWorkoutWeightsLifted
Body: null
Returns: HistoryExerciseModel
```

## Data Models

### GetRecommendationForExerciseModel

```typescript
interface GetRecommendationForExerciseModel {
  Username: string
  ExerciseId: number
  WorkoutId?: number
  IsQuickMode?: boolean
  LightSessionDays?: number
  SwapedExId?: number
  IsStrengthPhashe: boolean
  IsFreePlan: boolean
  IsFirstWorkoutOfStrengthPhase: boolean
  VersionNo: number
}
```

### RecommendationModel

```typescript
interface RecommendationModel {
  Series: number // Number of sets
  Reps: number // Number of reps
  Weight: MultiUnityWeight // Weight recommendation
  OneRMProgress: number
  RecommendationInKg: number
  OneRMPercentage: number
  WarmUpsList: WarmUp[] // Warmup sets
  RpRest: number // Rest time between sets
  IsBodyweight: boolean
  IsNormalSets: boolean
  IsDeload: boolean
  IsBackOffSet: boolean
  BackOffSetWeight?: MultiUnityWeight
  IsPyramid: boolean
  IsReversePyramid: boolean
  MinReps: number
  MaxReps: number
  RIR?: number // Reps in Reserve
  // Equipment flags
  isPlateAvailable: boolean
  isDumbbellAvailable: boolean
  isPulleyAvailable: boolean
  isBandsAvailable: boolean
}

interface MultiUnityWeight {
  Kg: number
  Lb: number
}

interface WarmUp {
  WarmUpReps: number
  WarmUpWeightSet: MultiUnityWeight
}
```

## Implementation Flow

### Step 1: Home Page Loading

```javascript
// On home page load
async function loadHomePageData() {
  // Get user's timezone
  const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone

  // Fetch basic workout program info
  const workoutProgram = await fetch(
    '/api/Exercise/GetUserWorkoutProgramTimeZoneInfo',
    {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ timeZone }),
    }
  )

  const programData = await workoutProgram.json()

  // Store program info
  const nextWorkout =
    programData.GetUserProgramInfoResponseModel.NextWorkoutTemplate
  const recommendedProgram =
    programData.GetUserProgramInfoResponseModel.RecommendedProgram

  // Display workout name and program on home page
  displayWorkoutInfo(nextWorkout, recommendedProgram)

  // DO NOT load exercise recommendations here
}
```

### Step 2: Start Workout - Navigate to Workout Page

```javascript
async function startWorkout() {
  // Check equipment settings
  const equipment = await checkUserEquipment()

  // Navigate to workout page with workout ID
  navigateToWorkoutPage(nextWorkout.Id)
}
```

### Step 3: Workout Page - Load All Exercise Recommendations

```javascript
async function loadWorkoutPage(workoutId) {
  // Get workout exercises
  const exercises = await getWorkoutExercises(workoutId)

  // Load recommendations for ALL exercises in parallel
  const recommendationPromises = exercises.map((exercise) =>
    loadExerciseRecommendation(exercise)
  )

  // Wait for all recommendations to load
  const recommendations = await Promise.all(recommendationPromises)

  // Store recommendations in memory
  exercises.forEach((exercise, index) => {
    exercise.recommendation = recommendations[index]
  })

  // Display first exercise
  displayExercise(exercises[0])
}

async function loadExerciseRecommendation(exercise) {
  const requestBody = {
    Username: userEmail,
    ExerciseId: exercise.Id,
    WorkoutId: currentWorkoutId,
    IsQuickMode: isQuickMode || false,
    LightSessionDays: lightSessionDays,
    IsStrengthPhashe: isStrengthPhase || false,
    IsFreePlan: isFreePlan || false,
    IsFirstWorkoutOfStrengthPhase: isFirstWorkoutOfStrengthPhase || false,
    VersionNo: 1,
  }

  // Determine which endpoint to use based on set style
  const endpoint =
    exercise.SetStyle === 'Normal' || exercise.IsFlexibility
      ? '/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew'
      : '/api/Exercise/GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew'

  const response = await fetch(endpoint, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(requestBody),
  })

  return await response.json()
}
```

### Step 4: Display Exercise with Recommendations

```javascript
function displayExercise(exercise) {
  const recommendation = exercise.recommendation

  // Display warmup sets
  recommendation.WarmUpsList.forEach((warmup, index) => {
    displaySet({
      setNumber: `W${index + 1}`,
      reps: warmup.WarmUpReps,
      weight: warmup.WarmUpWeightSet,
      isWarmup: true,
    })
  })

  // Display working sets
  for (let i = 0; i < recommendation.Series; i++) {
    displaySet({
      setNumber: i + 1,
      reps: recommendation.Reps,
      weight: recommendation.Weight,
      isWarmup: false,
      restTime: recommendation.RpRest,
    })
  }
}
```

### Step 5: Handle Pyramid and Reverse Pyramid Sets

```javascript
function calculatePyramidSets(recommendation, completedSets) {
  const sets = []

  if (recommendation.IsPyramid) {
    // Each set increases weight by 10%, decreases reps
    completedSets.forEach((prevSet, index) => {
      const weightMultiplier = 1 + 0.1 * (index + 1)
      const newWeight = prevSet.weight * weightMultiplier
      const newReps = Math.max(recommendation.MinReps, prevSet.reps - 1)

      sets.push({ weight: newWeight, reps: newReps })
    })
  } else if (recommendation.IsReversePyramid) {
    // Each set decreases weight by 10%, may increase reps
    completedSets.forEach((prevSet, index) => {
      const weightMultiplier = 1 - 0.1 * (index + 1)
      const newWeight = prevSet.weight * weightMultiplier
      const newReps = Math.min(recommendation.MaxReps, prevSet.reps + 1)

      sets.push({ weight: newWeight, reps: newReps })
    })
  }

  return sets
}
```

### Step 6: Update Recommendations After Set Completion

```javascript
async function completeSet(exerciseId, setData) {
  // Save set data locally
  saveSetToLocalStorage(exerciseId, setData)

  // Update next set recommendation if pyramid/reverse pyramid
  if (
    currentExercise.recommendation.IsPyramid ||
    currentExercise.recommendation.IsReversePyramid
  ) {
    updateNextSetRecommendation(currentExercise, setData)
  }

  // Check if exercise is complete
  if (isExerciseComplete(exerciseId)) {
    moveToNextExercise()
  }
}
```

### Step 7: Caching Strategy

```javascript
// Cache recommendations in localStorage
function cacheRecommendations(workoutId, recommendations) {
  const cacheKey = `workout_${workoutId}_${new Date().toDateString()}`
  localStorage.setItem(cacheKey, JSON.stringify(recommendations))
}

// Check cache before loading
async function loadWorkoutWithCache(workoutId) {
  const cacheKey = `workout_${workoutId}_${new Date().toDateString()}`
  const cached = localStorage.getItem(cacheKey)

  if (cached && !isWorkoutInProgress()) {
    return JSON.parse(cached)
  }

  // Load fresh recommendations
  return await loadWorkoutPage(workoutId)
}
```

## Key Implementation Notes

2. **Parallel Loading**: Load all exercise recommendations in parallel when entering the workout page for better performance.

3. **Set Styles**: Use different API endpoints based on exercise set style (Normal vs Rest-Pause).

4. **Equipment Adjustments**: Apply equipment-specific weight calculations after receiving recommendations.

5. **Progressive Overload**: The API automatically calculates weight/rep progressions based on previous workout history.

6. **Rest Times**: Use the `RpRest` value from recommendations to set rest timers between sets.

7. **Warmup Sets**: Always display warmup sets before working sets.

8. **Error Handling**: Implement retry logic for failed API calls with exponential backoff.

This implementation ensures the web app matches the mobile app's loading behavior while optimizing for web performance.
