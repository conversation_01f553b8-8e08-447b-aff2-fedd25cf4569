'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { SuccessIcon } from './SuccessIcon'

interface WorkoutSuccessScreenProps {
  exerciseCount: number
}

export function WorkoutSuccessScreen({
  exerciseCount,
}: WorkoutSuccessScreenProps) {
  const router = useRouter()
  const [showCheckmark] = useState(true)
  const [showMessage, setShowMessage] = useState(false)

  useEffect(() => {
    // Show message after 500ms
    const messageTimer = setTimeout(() => setShowMessage(true), 500)
    // Navigate to program page after 2 seconds
    const navigateTimer = setTimeout(() => {
      router.push('/program')
    }, 2000)

    return () => {
      clearTimeout(messageTimer)
      clearTimeout(navigateTimer)
    }
  }, [router])

  return (
    <div
      data-testid="workout-success-screen"
      className="min-h-screen flex flex-col items-center justify-center bg-white px-6"
    >
      {showCheckmark && <SuccessIcon size={120} />}
      {showMessage && (
        <div className="text-center mt-6 animate-fade-in">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Nice work!</h1>
          <p className="text-lg text-gray-600">
            {exerciseCount} {exerciseCount === 1 ? 'exercise' : 'exercises'}{' '}
            done
          </p>
        </div>
      )}
    </div>
  )
}
