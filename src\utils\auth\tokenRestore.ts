import { setAuthToken } from '@/api/client'

/**
 * Attempts to restore the auth token from the server-side cookie
 * This is called on app initialization to set the Authorization header
 * if the user has a valid session
 */
export async function restoreAuthToken(): Promise<boolean> {
  try {
    // Only run on client side
    if (typeof window === 'undefined') {
      return false
    }

    // Fetch the auth token from the server
    const response = await fetch('/api/auth/token', {
      credentials: 'include',
    })

    if (!response.ok) {
      console.warn('[Auth] Failed to retrieve auth token from server')
      return false
    }

    const data = await response.json()

    if (data.authenticated && data.token) {
      // Set the Authorization header in the API client
      setAuthToken(data.token)
      // eslint-disable-next-line no-console
      console.info('[Auth] Auth token restored successfully')
      return true
    }

    return false
  } catch (error) {
    console.error('[Auth] Error restoring auth token:', error)
    return false
  }
}
