'use client'

import { useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { useWorkoutStore } from '@/stores/workoutStore'

export function useNavigationGuard() {
  const router = useRouter()
  const { workoutSession } = useWorkoutStore()

  const handleBeforeUnload = useCallback(
    (e: BeforeUnloadEvent) => {
      if (workoutSession && !workoutSession.endTime) {
        e.preventDefault()
        e.returnValue = ''
      }
    },
    [workoutSession]
  )

  useEffect(() => {
    // Add beforeunload listener
    window.addEventListener('beforeunload', handleBeforeUnload)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [handleBeforeUnload])

  const navigateWithConfirmation = useCallback(
    (url: string) => {
      if (workoutSession && !workoutSession.endTime) {
        // TODO: Replace with custom modal component
        // For now, using browser confirm as a temporary solution
        // eslint-disable-next-line no-alert
        const confirmed = window.confirm(
          'You have an active workout. Are you sure you want to leave? Your progress will be saved.'
        )
        if (confirmed) {
          router.push(url)
        }
      } else {
        router.push(url)
      }
    },
    [workoutSession, router]
  )

  return {
    navigateWithConfirmation,
  }
}
