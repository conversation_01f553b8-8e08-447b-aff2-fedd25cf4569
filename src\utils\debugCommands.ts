import { SyncManager } from './syncManager'
import { logger } from './logger'

interface DebugCommands {
  clearSync: () => void
  syncStatus: () => number
  clearLocalStorage: (key?: string) => void
  showLocalStorage: () => void
}

declare global {
  interface Window {
    drmuscle?: DebugCommands
  }
}

/**
 * Debug commands exposed to window object in development
 */
export function initDebugCommands() {
  if (typeof window === 'undefined') return

  // Only expose in development or when debug mode is enabled
  const isDebug = localStorage.getItem('drmuscle-debug') === 'true'
  if (process.env.NODE_ENV !== 'development' && !isDebug) return

  // Expose debug commands
  window.drmuscle = {
    // Clear all sync data
    clearSync: () => {
      SyncManager.clearAll()
      logger.log('✅ Sync queue cleared')
    },
    // Get sync status
    syncStatus: () => {
      const count = SyncManager.getPendingCount()
      logger.log(`📊 Pending sync items: ${count}`)
      return count
    },
    // Clear specific localStorage items
    clearLocalStorage: (key?: string) => {
      if (key) {
        localStorage.removeItem(key)
        logger.log(`✅ Cleared localStorage key: ${key}`)
      } else {
        // Clear all drmuscle keys
        const keys = Object.keys(localStorage).filter((k) =>
          k.startsWith('drmuscle-')
        )
        keys.forEach((k) => localStorage.removeItem(k))
        logger.log(`✅ Cleared ${keys.length} localStorage keys`)
      }
    },
    // Show all drmuscle localStorage keys
    showLocalStorage: () => {
      const keys = Object.keys(localStorage).filter((k) =>
        k.startsWith('drmuscle-')
      )
      keys.forEach((k) => {
        const value = localStorage.getItem(k)
        logger.log(`📦 ${k}:`, `${value?.substring(0, 100)}...`)
      })
    },
  }

  logger.log(
    '🛠️  Debug commands available: window.drmuscle.clearSync(), syncStatus(), clearLocalStorage(), showLocalStorage()'
  )
}
