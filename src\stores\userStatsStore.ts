import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { UserStats, defaultUserStats } from '@/types/userStats'
import { fetchUserStats } from '@/api/userStats'
import { fetchCompleteUserStats } from '@/api/userStatsComplete'
import { logger } from '@/utils/logger'

interface UserStatsState {
  // State
  stats: UserStats | null
  isLoading: boolean
  error: string | null
  lastFetchTime: number | null

  // Actions
  fetchStats: (forceRefresh?: boolean) => Promise<void>
  setStats: (stats: UserStats) => void
  clearStats: () => void
  clearError: () => void

  // Cache helpers
  isStale: () => boolean
  hasData: () => boolean
}

// Cache TTL: 1 hour (same as progress data in the plan)
const CACHE_TTL = 60 * 60 * 1000 // 1 hour in milliseconds

export const useUserStatsStore = create<UserStatsState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        stats: null,
        isLoading: false,
        error: null,
        lastFetchTime: null,

        // Fetch stats from API
        fetchStats: async (forceRefresh = false) => {
          const state = get()

          // Skip if already loading
          if (state.isLoading) {
            logger.log('[UserStatsStore] Already loading, skipping fetch')
            return
          }

          // Skip if data is fresh (unless forced)
          if (!forceRefresh && !state.isStale() && state.hasData()) {
            logger.log('[UserStatsStore] Data is fresh, skipping fetch', {
              hasData: state.hasData(),
              isStale: state.isStale(),
              lastFetchTime: state.lastFetchTime,
              currentTime: Date.now(),
              age: state.lastFetchTime
                ? Date.now() - state.lastFetchTime
                : null,
            })
            return
          }

          set({ isLoading: true, error: null })

          try {
            logger.log('[UserStatsStore] Fetching user stats...')

            // Try the complete stats API first (mobile app pattern)
            let stats: UserStats
            try {
              logger.log('[UserStatsStore] Attempting complete stats fetch...')
              stats = await fetchCompleteUserStats()
              logger.log(
                '[UserStatsStore] Complete stats fetched successfully:',
                stats
              )
            } catch (completeError) {
              // Fall back to original API if complete stats fail
              logger.warn(
                '[UserStatsStore] Complete stats failed, falling back to original API:',
                completeError
              )
              stats = await fetchUserStats()
              logger.log('[UserStatsStore] Fallback stats fetched:', stats)
            }

            set({
              stats,
              isLoading: false,
              error: null,
              lastFetchTime: Date.now(),
            })

            logger.log('[UserStatsStore] Final stats stored:', stats)
          } catch (error) {
            const errorMessage =
              error instanceof Error ? error.message : 'Failed to fetch stats'

            logger.error('[UserStatsStore] Error fetching stats:', error)

            set({
              isLoading: false,
              error: errorMessage,
            })
          }
        },

        // Set stats directly (useful for optimistic updates)
        setStats: (stats) => {
          set({
            stats,
            lastFetchTime: Date.now(),
            error: null,
          })
        },

        // Clear stats
        clearStats: () => {
          set({
            stats: null,
            lastFetchTime: null,
            error: null,
          })
        },

        // Clear error
        clearError: () => {
          set({ error: null })
        },

        // Check if cache is stale
        isStale: () => {
          const { lastFetchTime } = get()
          if (!lastFetchTime) return true

          const age = Date.now() - lastFetchTime
          return age > CACHE_TTL
        },

        // Check if we have data
        hasData: () => {
          const { stats } = get()
          return stats !== null
        },
      }),
      {
        name: 'user-stats-storage',
        partialize: (state) => ({
          stats: state.stats,
          lastFetchTime: state.lastFetchTime,
        }),
      }
    )
  )
)

// Selectors for common use cases
export const selectUserStats = (state: UserStatsState) =>
  state.stats || defaultUserStats
export const selectIsLoadingStats = (state: UserStatsState) => state.isLoading
export const selectStatsError = (state: UserStatsState) => state.error
export const selectHasStatsData = (state: UserStatsState) => state.hasData()
export const selectIsStatsStale = (state: UserStatsState) => state.isStale()
