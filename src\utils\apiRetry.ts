import { retryWithBackoff, type RetryOptions } from './retry'
import { trackUserInfoFetch, trackStatsFetch } from './userInfoPerformance'
import { AxiosError } from 'axios'

/**
 * API Retry Configuration
 *
 * Provides consistent retry logic for UserInfo and Stats API calls
 * with performance tracking and proper error handling
 */

// Default retry options for UserInfo API
export const USER_INFO_RETRY_OPTIONS: RetryOptions = {
  maxRetries: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 4000, // 4 seconds
  factor: 2,
  jitter: 0.2,
  onRetry: (error, attempt) => {
    // Track retry attempt in performance monitoring
    trackUserInfoFetch(true) // isRetry = true

    console.warn(`[UserInfo API] Retry attempt ${attempt}/3:`, error.message)
  },
}

// Default retry options for Stats API
export const STATS_RETRY_OPTIONS: RetryOptions = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 4000,
  factor: 2,
  jitter: 0.2,
  onRetry: (error, attempt) => {
    // Track retry attempt in performance monitoring
    trackStatsFetch(true) // isRetry = true

    console.warn(`[Stats API] Retry attempt ${attempt}/3:`, error.message)
  },
}

/**
 * Wrap an API call with retry logic and timeout
 *
 * @param apiCall The API call function
 * @param options Retry options
 * @param timeoutMs Timeout in milliseconds (default: 10000)
 * @returns Promise with the API response
 */
export async function retryApiCall<T>(
  apiCall: () => Promise<T>,
  options: RetryOptions = USER_INFO_RETRY_OPTIONS,
  timeoutMs = 10000
): Promise<T> {
  // Create abort controller for timeout
  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), timeoutMs)

  try {
    // Wrap the API call with abort signal support
    const callWithAbort = async () => {
      if (controller.signal.aborted) {
        throw new Error('Request timeout')
      }

      try {
        return await apiCall()
      } catch (error) {
        // Check if it's an Axios error to get more details
        if (error instanceof Error && 'response' in error) {
          const axiosError = error as AxiosError

          // Don't retry on 4xx errors (except 408 and 429)
          if (
            axiosError.response?.status &&
            axiosError.response.status >= 400 &&
            axiosError.response.status < 500 &&
            axiosError.response.status !== 408 &&
            axiosError.response.status !== 429
          ) {
            // Create a non-retryable error
            const nonRetryableError = new Error(error.message) as Error & {
              originalError?: Error
              status?: number
            }
            nonRetryableError.name = 'NonRetryableError'
            nonRetryableError.originalError = error
            nonRetryableError.status = axiosError.response.status
            throw nonRetryableError
          }
        }

        throw error
      }
    }

    // Execute with retry logic
    const result = await retryWithBackoff(callWithAbort, {
      ...options,
      signal: controller.signal,
    })

    clearTimeout(timeoutId)
    return result
  } catch (error) {
    clearTimeout(timeoutId)

    // Handle timeout specifically
    if (
      controller.signal.aborted ||
      (error instanceof Error && error.message === 'Request timeout')
    ) {
      const timeoutError = new Error(`API call timed out after ${timeoutMs}ms`)
      timeoutError.name = 'TimeoutError'
      throw timeoutError
    }

    // If it's our non-retryable error wrapper, throw the original error
    if (
      error instanceof Error &&
      error.name === 'NonRetryableError' &&
      (error as Error & { originalError?: Error }).originalError
    ) {
      const { originalError } = error as Error & { originalError?: Error }
      if (originalError instanceof Error) {
        throw originalError
      }
    }

    throw error
  }
}

/**
 * Check if an API error should trigger a cache clear
 *
 * @param error The error to check
 * @returns Whether cache should be cleared
 */
export function shouldClearCacheOnError(error: unknown): boolean {
  if (!(error instanceof Error)) return false

  // Check for Axios error with status
  if (
    'response' in error &&
    typeof (error as AxiosError).response === 'object'
  ) {
    const axiosError = error as AxiosError
    const status = axiosError.response?.status

    // Clear cache on authentication errors
    if (status === 401 || status === 403) return true

    // Don't clear cache on temporary errors
    if (status && (status >= 500 || status === 408 || status === 429))
      return false
  }

  // Don't clear cache on network/timeout errors
  if (
    error.name === 'TimeoutError' ||
    error.name === 'NetworkError' ||
    error.message.toLowerCase().includes('network') ||
    error.message.toLowerCase().includes('timeout')
  ) {
    return false
  }

  // Clear cache for other errors
  return true
}
